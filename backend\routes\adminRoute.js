import express from "express";
import { addDoctor } from "../controllers/adminController.js";
import upload from "../middleware/multer.js";

const adminRouter = express.Router();

console.log("Admin router loaded");

// Route with multer middleware to handle form-data
adminRouter.post("/add-doctor", upload.single("image"), addDoctor);

console.log("Admin route /add-doctor registered");

export default adminRouter;
