import express from "express";
import { addDoctor } from "../controllers/adminController.js";
import upload from "../middleware/multer.js";

const adminRouter = express.Router();

console.log("Admin router loaded");

// Temporarily test without multer middleware
adminRouter.post("/add-doctor", addDoctor);

// Original route with multer (commented out for testing)
// adminRouter.post("/add-doctor", upload.single("image"), addDoctor);

console.log("Admin route /add-doctor registered");

export default adminRouter;
