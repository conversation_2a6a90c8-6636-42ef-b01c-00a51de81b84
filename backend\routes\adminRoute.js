import express from "express";
import { addDoctor } from "../controllers/adminController.js";
import upload from "../middleware/multer.js";

const adminRouter = express.Router();

console.log("Admin router loaded");

// Route with multer middleware to handle form-data
adminRouter.post(
	"/add-doctor",
	(req, res, next) => {
		console.log(
			"Before multer - Content-Type:",
			req.headers["content-type"]
		);
		next();
	},
	upload.single("image"),
	(req, res, next) => {
		console.log("After multer - File:", req.file);
		console.log("After multer - Body:", req.body);
		next();
	},
	addDoctor
);

console.log("Admin route /add-doctor registered");

export default adminRouter;
