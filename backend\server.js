import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import connectDB from "./config/connection.js";
import connectCLoudianry from "./config/cloudinary.js";
import adminRouter from "./routes/adminRoute.js";

dotenv.config();
const app = express();

app.use(express.json());
app.use(cors());

connectDB();
connectCLoudianry();

// Add logging middleware to see all requests
app.use((req, res, next) => {
	console.log(`${req.method} ${req.path}`);
	next();
});

// Add a test route to verify server is working
app.get("/test", (req, res) => {
	res.json({ message: "Server is working!" });
});

app.use("/api/admin", adminRouter);

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
	console.log(`Server running on port ${PORT}`);
});
