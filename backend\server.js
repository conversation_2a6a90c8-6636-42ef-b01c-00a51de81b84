import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import connectDB from "./config/connection.js";
import connectCLoudianry from "./config/cloudinary.js";
import adminRouter from "./routes/adminRoute.js";

dotenv.config();
const app = express();

app.use(express.json());
app.use(cors());

connectDB();
connectCLoudianry();

app.use("/api/admin", adminRouter);

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
	console.log(`Server running on port ${PORT}`);
});
