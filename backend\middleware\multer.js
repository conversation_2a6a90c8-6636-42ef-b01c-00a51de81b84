import multer from "multer";

// Use memory storage for testing
const storage = multer.memoryStorage();

const upload = multer({
	storage: storage,
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
	},
	fileFilter: function (req, file, callback) {
		console.log(
			"Multer fileFilter - File received:",
			file.originalname,
			file.mimetype
		);
		// Check if file is an image
		if (file.mimetype.startsWith("image/")) {
			callback(null, true);
		} else {
			callback(new Error("Only image files are allowed!"), false);
		}
	},
});

export default upload;
