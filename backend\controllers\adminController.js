import validator from "validator";
import bcrypt from "bcryptjs";
import { v2 as cloudinary } from "cloudinary";

// Api for adding doctor
export const addDoctor = async (req, res) => {
	try {
		const {
			name,
			email,
			password,
			speciality,
			experience,
			degree,
			fees,
			about,
			address,
		} = req.body;

		const imageFile = req.file;

		// checking for all data to add doctor
		if (
			!name ||
			!email ||
			!password ||
			!speciality ||
			!experience ||
			!degree ||
			!fees ||
			!about ||
			!address ||
			!imageFile
		) {
			return res.json({
				success: false,
				message: "All fields are required",
			});
		}

		// validating email format
		if (!validator.isEmail(email)) {
			return res.json({
				success: false,
				message: "Please enter a valid email",
			});
		}

		// validating password length
		if (password.length < 8) {
			return res.json({
				success: false,
				message: "Password should be more than 8 characters",
			});
		}

		// hashing doctor password
		const salt = await bcrypt.genSalt(10);
		const hashedPassword = await bcrypt.hash(password, salt);

		// upload image to cloudinary
		const imageUpload = await cloudinary.uploader.upload(imageFile.path, {
			resource_type: "image",
		});
		const imageUrl = imageUpload.secure_url;

		const doctorData = {
			name,
			email,
			image: imageUrl,
			password: hashedPassword,
			speciality,
			experience,
			degree,
			fees,
			about,
			address: JSON.parse(address),
			date: Date.now(),
		};

		// save doctor data to database
		const newDoctor = new Doctor(doctorData);
		await newDoctor.save();

		res.json({ success: true, message: "Doctor added successfully" });
	} catch (error) {
		console.log("Error:", error);
		res.json({ success: false, message: error.message });
	}
};
