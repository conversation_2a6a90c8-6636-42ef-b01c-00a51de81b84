import validator from "validator";
import bcrypt from "bcryptjs";
import { v2 as cloudinary } from "cloudinary";

// Api for adding doctor
export const addDoctor = async (req, res) => {
	try {
		const {
			name,
			email,
			password,
			speciality,
			experience,
			degree,
			fees,
			about,
			address,
		} = req.body;

		const imageFile = req.file;

		// Debug logging
		console.log("Request body:", req.body);
		console.log("Request file:", req.file);
		console.log("All fields check:", {
			name: !!name,
			email: !!email,
			password: !!password,
			speciality: !!speciality,
			experience: !!experience,
			degree: !!degree,
			fees: !!fees,
			about: !!about,
			address: !!address,
			imageFile: !!imageFile,
		});

		// checking for all data to add doctor (temporarily make image optional)
		if (
			!name ||
			!email ||
			!password ||
			!speciality ||
			!experience ||
			!degree ||
			!fees ||
			!about ||
			!address
		) {
			return res.json({
				success: false,
				message: "All fields are required",
			});
		}

		// Check if image is provided
		if (!imageFile) {
			return res.json({
				success: false,
				message:
					"Image file is required. Please make sure the file field name is 'image' and type is 'File' in Thunder Client",
			});
		}

		// validating email format
		if (!validator.isEmail(email)) {
			return res.json({
				success: false,
				message: "Please enter a valid email",
			});
		}

		// validating password length
		if (password.length < 8) {
			return res.json({
				success: false,
				message: "Password should be more than 8 characters",
			});
		}

		// hashing doctor password
		const salt = await bcrypt.genSalt(10);
		const hashedPassword = await bcrypt.hash(password, salt);

		// upload image to cloudinary (using buffer for memory storage)
		const imageUpload = await cloudinary.uploader.upload(
			`data:${imageFile.mimetype};base64,${imageFile.buffer.toString(
				"base64"
			)}`,
			{
				resource_type: "image",
			}
		);
		const imageUrl = imageUpload.secure_url;

		const doctorData = {
			name,
			email,
			image: imageUrl,
			password: hashedPassword,
			speciality,
			experience,
			degree,
			fees,
			about,
			address: JSON.parse(address),
			date: Date.now(),
		};

		// save doctor data to database
		const newDoctor = new Doctor(doctorData);
		await newDoctor.save();

		res.json({ success: true, message: "Doctor added successfully" });
	} catch (error) {
		console.log("Error:", error);
		res.json({ success: false, message: error.message });
	}
};
