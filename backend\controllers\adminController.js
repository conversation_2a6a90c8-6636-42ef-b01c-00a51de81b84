// Api for adding doctor
export const addDoctor = async (req, res) => {
	try {
		// Debug logging
		console.log("Request body:", req.body);
		console.log("Request file:", req.file);
		console.log("Content-Type:", req.headers["content-type"]);

		// Check if req.body exists
		if (!req.body) {
			return res.json({
				success: false,
				message: "Request body is missing",
			});
		}

		const {
			name,
			email,
			password,
			speciality,
			experience,
			degree,
			fees,
			about,
			address,
		} = req.body;

		const imageFile = req.file;

		console.log("Extracted data:", {
			name,
			email,
			password,
			speciality,
			experience,
			degree,
			fees,
			about,
			address,
			image: imageFile,
		});

		// Send success response
		res.json({
			success: true,
			message: "Doctor added successfully",
			data: {
				name,
				email,
				speciality,
				experience,
				degree,
				fees,
				about,
				address,
			},
		});
	} catch (error) {
		console.log("Error:", error);
		res.json({ success: false, message: error.message });
	}
};
